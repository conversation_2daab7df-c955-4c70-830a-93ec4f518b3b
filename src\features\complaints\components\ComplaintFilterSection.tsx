'use client';

import { FilterSection } from '@/components/filter-section';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, X } from 'lucide-react';

export interface ComplaintFilters {
  search?: string;
  status?: string;
  followUp?: string;
  dateRange?: {
    from?: Date;
    to?: Date;
  };
}

const COMPLAINT_STATUSES = ['open', 'on_hold', 'closed'] as const;
const FOLLOW_UP_STATUSES = ['in_progress', 'pending_approval', 'verified'] as const;

export function ComplaintFilterSection({
  filters,
  onFilterChange,
}: {
  filters: ComplaintFilters;
  onFilterChange: (filters: ComplaintFilters) => void;
}) {
  // Render filter fields for the popover
  const renderFilters = (
    <>
      {/* Status Filter */}
      <div className="space-y-3">
        <label className="text-sm font-medium text-gray-700">Status</label>
        <Select
          value={filters.status || 'all'}
          onValueChange={(value) =>
            onFilterChange({
              ...filters,
              status: value === 'all' ? undefined : value,
            })
          }
        >
          <SelectTrigger className="w-full border-gray-300 focus:border-gray-400 rounded-lg">
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            {COMPLAINT_STATUSES.map((status) => (
              <SelectItem key={status} value={status}>
                <div className="flex items-center gap-2">
                  <div
                    className={cn(
                      'w-2 h-2 rounded-full',
                      status === 'open' && 'bg-green-500',
                      status === 'on_hold' && 'bg-orange-500',
                      status === 'closed' && 'bg-red-500',
                    )}
                  />
                  <span className="capitalize">{status.replace('_', ' ')}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Follow Up Filter */}
      <div className="space-y-3">
        <label className="text-sm font-medium text-gray-700">Follow Up</label>
        <Select
          value={filters.followUp || 'all'}
          onValueChange={(value) =>
            onFilterChange({
              ...filters,
              followUp: value === 'all' ? undefined : value,
            })
          }
        >
          <SelectTrigger className="w-full border-gray-300 focus:border-gray-400 rounded-lg">
            <SelectValue placeholder="Select follow up" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Follow Up</SelectItem>
            {FOLLOW_UP_STATUSES.map((followUp) => (
              <SelectItem key={followUp} value={followUp}>
                <div className="flex items-center gap-2">
                  <div
                    className={cn(
                      'w-2 h-2 rounded-full',
                      followUp === 'in_progress' && 'bg-blue-500',
                      followUp === 'pending_approval' && 'bg-yellow-500',
                      followUp === 'verified' && 'bg-purple-500',
                    )}
                  />
                  <span className="capitalize">{followUp.replace('_', ' ')}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Date Range */}
      <div className="space-y-3">
        <label className="text-sm font-medium text-gray-700">Date Range</label>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'w-full justify-start text-left font-normal border-gray-300 hover:border-gray-400 rounded-lg',
                !filters.dateRange?.from && 'text-gray-500',
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {filters.dateRange?.from ? (
                filters.dateRange.to ? (
                  <>
                    {format(filters.dateRange.from, 'MMM dd')} -{' '}
                    {format(filters.dateRange.to, 'MMM dd')}
                  </>
                ) : (
                  format(filters.dateRange.from, 'MMM dd, yyyy')
                )
              ) : (
                'Select date range'
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="range"
              defaultMonth={filters.dateRange?.from}
              selected={{
                from: filters.dateRange?.from,
                to: filters.dateRange?.to,
              }}
              onSelect={(range) =>
                onFilterChange({ ...filters, dateRange: range || {} })
              }
              numberOfMonths={2}
            />
          </PopoverContent>
        </Popover>
      </div>
    </>
  );

  // Render active filter badges
  const getActiveFilterBadges = (
    filters: ComplaintFilters,
    onFilterChange: (filters: ComplaintFilters) => void,
  ) => (
    <>
      {filters.status && (
        <Badge
          variant="secondary"
          className="h-7 text-xs bg-emerald-100 text-emerald-700 border border-emerald-200 rounded-lg"
        >
          Status: {filters.status}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onFilterChange({ ...filters, status: undefined })}
            className="ml-1 h-auto p-0 hover:bg-transparent"
          >
            <X className="h-3 w-3" />
          </Button>
        </Badge>
      )}
      {filters.followUp && (
        <Badge
          variant="secondary"
          className="h-7 text-xs bg-blue-100 text-blue-700 border border-blue-200 rounded-lg"
        >
          Follow Up: {filters.followUp}
          <Button
            variant="ghost"
            size="sm"
            onClick={() =>
              onFilterChange({ ...filters, followUp: undefined })
            }
            className="ml-1 h-auto p-0 hover:bg-transparent"
          >
            <X className="h-3 w-3" />
          </Button>
        </Badge>
      )}
      {filters.dateRange?.from && (
        <Badge
          variant="secondary"
          className="h-7 text-xs bg-amber-100 text-amber-700 border border-amber-200 rounded-lg"
        >
          Date: {format(filters.dateRange.from, 'MMM dd')}
          {filters.dateRange.to &&
            ` - ${format(filters.dateRange.to, 'MMM dd')}`}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onFilterChange({ ...filters, dateRange: {} })}
            className="ml-1 h-auto p-0 hover:bg-transparent"
          >
            <X className="h-3 w-3" />
          </Button>
        </Badge>
      )}
    </>
  );

  return (
    <FilterSection
      filters={filters}
      onFilterChange={onFilterChange}
      renderFilters={renderFilters}
      getActiveFilterBadges={getActiveFilterBadges}
      searchPlaceholder="Search complaints..."
    />
  );
}