'use client';

import { updateLastLogin } from '@/lib/database';
import { supabase } from '@/lib/supabase-enhanced';
import type { AuthError, Session } from '@supabase/supabase-js';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useCallback } from 'react';
import type {
  LoginCredentials,
  SignUpCredentials,
  UserRole,
} from '../types/auth';

// Hook to get current user
export function useUser() {
  return useQuery({
    queryKey: ['user'],
    queryFn: async () => {
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser();
      if (error) throw error;
      return user;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to get current user with profile data
export function useUserWithProfile() {
  return useQuery({
    queryKey: ['user-with-profile'],
    queryFn: async () => {
      // Get auth user
      const {
        data: { user },
        error: authError,
      } = await supabase.auth.getUser();

      if (authError) throw authError;
      if (!user) return null;

      // Get profile data
      const { data: profile, error: profileError } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();

      if (profileError && profileError.code !== 'PGRST116') {
        // PGRST116 is "not found" - acceptable if profile doesn't exist yet
        throw profileError;
      }

      return {
        ...user,
        profile: profile || null,
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to get current session
export function useSession() {
  return useQuery({
    queryKey: ['session'],
    queryFn: async () => {
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession();
      if (error) throw error;
      return session;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for login
export function useLogin() {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationKey: ['auth', 'login'],
    mutationFn: async ({ email, password }: LoginCredentials) => {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      return data;
    },
    onSuccess: async (data) => {
      // Update the user and session queries
      queryClient.setQueryData(['user'], data.user);
      queryClient.setQueryData(['session'], data.session);

      // Ensure session cookies are set for server-side requests
      if (data.session) {
        document.cookie = `sb-access-token=${data.session.access_token}; path=/; max-age=${60 * 60 * 24 * 7};`;
        document.cookie = `sb-refresh-token=${data.session.refresh_token}; path=/; max-age=${60 * 60 * 24 * 7};`;
      }

      // Check if profile exists, create if missing (fallback mechanism)
      if (data.user?.id) {
        const { data: existingProfile } = await supabase
          .from('users')
          .select('id, onboarding_completed, user_role')
          .eq('id', data.user.id)
          .single();

        if (!existingProfile && data.user.user_metadata) {
          // Create profile from user metadata if it doesn't exist
          const profileData = {
            id: data.user.id,
            email: data.user.email || '', // Required field
            name:
              data.user.user_metadata.full_name ||
              data.user.email?.split('@')[0] ||
              'Unknown',
            phone_number: data.user.user_metadata.phone_number || null,
            user_role: (data.user.user_metadata.role as UserRole) || 'Client',
            onboarding_completed: false, // New users need to complete onboarding
            created_at: new Date().toISOString(),
          };

          await supabase.from('users').insert(profileData);
        }

        // Update last login timestamp
        try {
          await updateLastLogin(data.user.id);
        } catch {
          // Don't throw - this is not critical for login success
        }

        // Check onboarding status and redirect accordingly
        if (existingProfile && !existingProfile.onboarding_completed) {
          // Check if user is admin - admins don't need onboarding, mark as completed
          const userRole = data.user.user_metadata?.role as UserRole;
          if (userRole === 'admin') {
            // Auto-complete onboarding for admin users
            await supabase
              .from('users')
              .update({ onboarding_completed: true })
              .eq('id', data.user.id);
          } else {
            // Non-admin users need to complete onboarding
            router.push('/profile');
            return;
          }
        }

        // Explicitly redirect to default page based on user role if onboarding is completed
        if (existingProfile && existingProfile.onboarding_completed) {
          // Ensure the locale is included in the redirect path
          const locale = window.location.pathname.split('/')[1] || 'en';
          const userRole =
            existingProfile.user_role ||
            (data.user.user_metadata?.role as UserRole) ||
            'contractor';
          let redirectPath = '/projects';
          if (userRole === 'admin') {
            redirectPath = '/admin/dashboard';
          } else if (userRole === 'viewer') {
            redirectPath = '/daily-logs';
          }
          router.push(`/${locale}${redirectPath}`);
        } else {
          router.refresh();
        }
      } else {
        router.refresh();
      }

      // Invalidate and refetch auth-related queries to ensure fresh data
      await queryClient.invalidateQueries({ queryKey: ['user'] });
      await queryClient.invalidateQueries({ queryKey: ['session'] });
      await queryClient.invalidateQueries({ queryKey: ['user-with-profile'] });
      await queryClient.invalidateQueries({ queryKey: ['profile'] });
    },
    onError: (_error: AuthError) => {
      // Error will be handled in the component
    },
  });
}

// Hook for signup with automatic profile creation
export function useSignUp() {
  const queryClient = useQueryClient();
  const router = useRouter();
  return useMutation({
    mutationKey: ['auth', 'signup'],
    mutationFn: async ({ email, password, role }: SignUpCredentials) => {
      // Always use NEXT_PUBLIC_SITE_URL to ensure consistency across environments
      const siteUrl = process.env.NEXT_PUBLIC_SITE_URL!;
      const redirectUrl = `${siteUrl}/auth/callback`;

      // Step 1: Create auth user with metadata
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            role: role,
          },
          emailRedirectTo: redirectUrl,
        },
      });

      if (error) {
        throw error;
      }

      // Step 2: Handle profile creation based on confirmation status
      // When email confirmations are enabled, profile creation is deferred until confirmation
      if (data.user && data.session) {
        // User is immediately confirmed (shouldn't happen with confirmations enabled, but handle gracefully)
        try {
          const { error: profileError } = await supabase.from('users').insert({
            id: data.user.id,
            email: email,
            name: email.split('@')[0], // Use email prefix as temporary name
            user_role: role,
            onboarding_completed: false, // New users need to complete onboarding
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });

          if (profileError) {
            // Don't throw - user is created, profile can be created later
          }
        } catch {
          // Don't throw - user account is created successfully
        }
      } else if (data.user && !data.session) {
        // User created but needs email confirmation (expected flow)
        // User will be redirected after email confirmation
      }

      return data;
    },
    onSuccess: (data, variables) => {
      // With email confirmations enabled, handle both confirmed and unconfirmed users
      if (data.user && data.session) {
        // User is immediately confirmed and has a session (edge case)
        queryClient.setQueryData(['user'], data.user);
        queryClient.setQueryData(['session'], data.session);

        // Invalidate profile-related queries
        queryClient.invalidateQueries({ queryKey: ['user-with-profile'] });
        queryClient.invalidateQueries({ queryKey: ['profile'] });

        // New users always need to complete onboarding
        router.push('/profile');
      } else if (data.user && !data.session) {
        // User created but needs email confirmation (expected flow)
        // Redirect to check email page with email and user type
        const checkEmailUrl = `/check-email?email=${encodeURIComponent(variables.email)}&type=${variables.role}`;
        router.push(checkEmailUrl);
      } else {
        // This should not happen when email confirmations are disabled
        // But handle it gracefully just in case
      }
    },
  });
}

// Hook for logout
export function useLogout() {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationKey: ['auth', 'logout'],
    mutationFn: async () => {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    },
    onSuccess: () => {
      // Clear all queries
      queryClient.clear();

      // Clear role and onboarding cookies (only on client)
      if (typeof document !== 'undefined') {
        document.cookie =
          'user_role=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
        document.cookie =
          'onboarding_completed=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
        // Clear Supabase session cookies to ensure complete logout
        document.cookie =
          'sb-access-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
        document.cookie =
          'sb-refresh-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
      }

      // Always redirect to landing page after logout, regardless of user role
      // This ensures users need to sign in again to access the application
      router.push('/');
      router.refresh();
    },
    onError: (_error: AuthError) => {
      // Logout error will be handled in the component
    },
  });
}

// Hook for password reset
export function usePasswordReset() {
  return useMutation({
    mutationKey: ['auth', 'password-reset'],
    mutationFn: async (email: string) => {
      // Always use NEXT_PUBLIC_SITE_URL to ensure consistency across environments
      const siteUrl = process.env.NEXT_PUBLIC_SITE_URL!;

      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${siteUrl}/reset-password`,
      });
      if (error) throw error;
    },
    onError: (_error: AuthError) => {
      // Password reset error will be handled in the component
    },
  });
}

// Hook for OTP verification
export function useVerifyOtp() {
  const router = useRouter();

  return useMutation({
    mutationKey: ['auth', 'verify-otp'],
    mutationFn: async ({ email, code }: { email: string; code: string }) => {
      const { data, error } = await supabase.auth.verifyOtp({
        type: 'recovery',
        email,
        token: code,
      });
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // On successful verification, redirect to reset password screen
      router.push('/reset-password');
    },
    onError: (_error: AuthError) => {
      // OTP verification error will be handled in the component
    },
  });
}

// Hook for updating password after OTP verification
export function useUpdatePassword() {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationKey: ['auth', 'update-password'],
    mutationFn: async (newPassword: string) => {
      const { data, error } = await supabase.auth.updateUser({
        password: newPassword,
      });
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // Clear all queries to ensure fresh state
      queryClient.clear();

      // Redirect to login page with success message
      router.push('/login');
    },
    onError: (_error: AuthError) => {
      // Password update error will be handled in the component
    },
  });
}

// Auth state listener hook with profile sync
export function useAuthStateChange() {
  const queryClient = useQueryClient();

  const handleAuthStateChange = useCallback(
    (event: string, session: Session | null) => {
      if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
        queryClient.setQueryData(['session'], session);
        queryClient.setQueryData(['user'], session?.user ?? null);

        // Invalidate profile-related queries to refetch fresh data
        queryClient.invalidateQueries({ queryKey: ['user-with-profile'] });
        queryClient.invalidateQueries({ queryKey: ['profile'] });

        // Update last login if user signed in
        if (event === 'SIGNED_IN' && session?.user?.id) {
          updateLastLogin(session.user.id).catch(() => {
            // Error updating last login - not critical
          });
        }
      } else if (event === 'SIGNED_OUT') {
        queryClient.setQueryData(['session'], null);
        queryClient.setQueryData(['user'], null);
        queryClient.setQueryData(['user-with-profile'], null);

        // Clear all profile-related cache
        queryClient.removeQueries({ queryKey: ['profile'] });
      }
    },
    [queryClient],
  );

  return { handleAuthStateChange };
}
