'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FilterSection } from '@/components/filter-section';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  ComplaintWithRelations,
  useComplaints,
  useUpdateComplaint,
} from '@/features/complaints/hooks/use-complaints-simple';
import {
  AlertCircle,
  AlertTriangle,
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  Clock,
  Download,
  Eye,
  TrendingUp,
  X,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { toast } from 'sonner';

export default function ComplaintLogPage() {
  const t = useTranslations('complaints');

  // Fetch real complaints data
  const { data: complaintsData, isLoading, error } = useComplaints();
  const updateComplaintMutation = useUpdateComplaint();

  // Filter states
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    pma: '',
    contractor: '',
    dateRange: {} as { from?: Date; to?: Date },
  });

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const handleFilterChange = (newFilters: typeof filters) => {
    setFilters(newFilters);
  };

  const handleReset = () => {
    setFilters({
      search: '',
      status: '',
      pma: '',
      contractor: '',
      dateRange: {},
    });
  };

  // Filter complaints based on admin needs
  const getFilteredComplaints = () => {
    if (!complaintsData) return [];

    let filtered = complaintsData.filter((complaint) => {
      // Admin should primarily see complaints that need approval
      return (
        complaint.follow_up === 'pending_approval' ||
        complaint.status === 'closed'
      ); // Show completed ones too
    });

    // Apply search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter((complaint) =>
        complaint.number?.toLowerCase().includes(searchTerm) ||
        complaint.description?.toLowerCase().includes(searchTerm) ||
        complaint.location?.toLowerCase().includes(searchTerm) ||
        complaint.no_pma_lif?.toLowerCase().includes(searchTerm) ||
        complaint.contractor_name?.toLowerCase().includes(searchTerm)
      );
    }

    // Apply additional filters
    if (filters.status) {
      filtered = filtered.filter(
        (complaint) =>
          complaint.follow_up === filters.status ||
          complaint.status === filters.status,
      );
    }

    if (filters.pma) {
      filtered = filtered.filter((complaint) =>
        complaint.no_pma_lif?.includes(filters.pma),
      );
    }

    if (filters.contractor) {
      filtered = filtered.filter((complaint) =>
        complaint.contractor_name
          ?.toLowerCase()
          .includes(filters.contractor.toLowerCase()),
      );
    }

    // Apply date range filter
    if (filters.dateRange?.from) {
      filtered = filtered.filter(
        (complaint) => new Date(complaint.date) >= filters.dateRange.from!,
      );
    }

    if (filters.dateRange?.to) {
      filtered = filtered.filter(
        (complaint) => new Date(complaint.date) <= filters.dateRange.to!,
      );
    }

    return filtered;
  };

  const filteredComplaints = getFilteredComplaints();

  // Calculate statistics
  const totalComplaints = complaintsData?.length || 0;
  const pendingApproval =
    complaintsData?.filter((c) => c.follow_up === 'pending_approval').length ||
    0;
  const verified =
    complaintsData?.filter((c) => c.follow_up === 'verified').length || 0;
  const inProgress =
    complaintsData?.filter((c) => c.follow_up === 'in_progress').length || 0;

  // Calculate weekly trend data from real complaints
  const getWeeklyTrendData = () => {
    if (!complaintsData) return [];

    // Get last 5 weeks of data
    const weeks = [];
    const now = new Date();

    for (let i = 4; i >= 0; i--) {
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - i * 7);
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);

      const weekComplaints = complaintsData.filter((complaint) => {
        const complaintDate = new Date(complaint.date);
        return complaintDate >= weekStart && complaintDate <= weekEnd;
      });

      weeks.push({
        date: `${weekStart.getDate()}/${weekStart.getMonth() + 1}`,
        count: weekComplaints.length,
      });
    }

    return weeks;
  };

  // Calculate status distribution data
  const getStatusDistribution = () => {
    if (!complaintsData) return [];

    const statusCounts = {
      'Pending Approval': pendingApproval,
      'In Progress': inProgress,
      Verified: verified,
      Others: totalComplaints - pendingApproval - inProgress - verified,
    };

    return Object.entries(statusCounts)
      .filter(([_, count]) => count > 0)
      .map(([name, value], index) => ({
        name,
        value,
        color: ['#F59E0B', '#3B82F6', '#10B981', '#6B7280'][index],
      }));
  };

  // Get overdue complaints (past expected completion date)
  const getOverdueComplaints = () => {
    if (!complaintsData) return [];

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return complaintsData.filter((complaint) => {
      if (!complaint.expected_completion_date) return false;

      const expectedDate = new Date(complaint.expected_completion_date);
      expectedDate.setHours(0, 0, 0, 0);

      // Overdue if: past expected completion date AND not yet completed/verified
      return (
        expectedDate < today &&
        complaint.follow_up !== 'verified' &&
        !complaint.actual_completion_date
      );
    });
  };

  const trendData = getWeeklyTrendData();
  const statusBreakdown = getStatusDistribution();
  const overdueComplaints = getOverdueComplaints();

  // Admin action to verify/approve a complaint
  const handleVerifyComplaint = async (complaint: ComplaintWithRelations) => {
    if (!complaint.id) return;

    try {
      const updatedData = {
        // Keep existing data
        email: complaint.email,
        date: new Date(complaint.date),
        expected_completion_date: complaint.expected_completion_date
          ? new Date(complaint.expected_completion_date)
          : new Date(),
        contractor_name: complaint.contractor_name || '',
        location: complaint.location || '',
        no_pma_lif: complaint.no_pma_lif || '',
        description: complaint.description || '',
        involves_mantrap: complaint.involves_mantrap || false,
        actual_completion_date: complaint.actual_completion_date
          ? new Date(complaint.actual_completion_date)
          : undefined,
        repair_completion_time: complaint.repair_completion_time || undefined,
        cause_of_damage: complaint.cause_of_damage || undefined,
        correction_action: complaint.correction_action || undefined,
        proof_of_repair_urls: complaint.proof_of_repair_urls || [],
        repair_cost: complaint.repair_cost || undefined,
        status: complaint.status,
        follow_up: 'verified' as const, // Change to verified
      };

      await updateComplaintMutation.mutateAsync({
        id: complaint.id,
        data: updatedData,
      });

      toast.success(t('actions.verifySuccess'));
    } catch (error) {
      console.error('Error verifying complaint:', error);
      toast.error(t('actions.verifyError'));
    }
  };

  const getStatusBadge = (status: string, followUp?: string) => {
    // For admin view, show follow-up status primarily
    const displayStatus = followUp || status;

    switch (displayStatus) {
      case 'pending_approval':
        return (
          <Badge
            variant="destructive"
            className="bg-yellow-100 text-yellow-800"
          >
            {t('status.pendingApproval')}
          </Badge>
        );
      case 'verified':
        return (
          <Badge variant="default" className="bg-green-100 text-green-800">
            {t('status.verified')}
          </Badge>
        );
      case 'in_progress':
        return (
          <Badge variant="default" className="bg-blue-100 text-blue-800">
            {t('status.inProgress')}
          </Badge>
        );
      case 'closed':
        return (
          <Badge variant="default" className="bg-gray-100 text-gray-800">
            {t('status.completed')}
          </Badge>
        );
      case 'open':
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800">
            {t('status.open')}
          </Badge>
        );
      default:
        return <Badge variant="outline">{displayStatus}</Badge>;
    }
  };

  // Pagination
  const totalPages = Math.ceil(filteredComplaints.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentData = filteredComplaints.slice(startIndex, endIndex);

  if (isLoading) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
            Loading complaints...
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex flex-col items-center gap-2 py-8">
          <AlertCircle className="h-12 w-12 text-red-500" />
          <p className="text-red-600">Failed to load complaints</p>
          <p className="text-sm text-muted-foreground">
            {error instanceof Error ? error.message : 'Unknown error occurred'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{t('title')}</h1>
          <p className="text-sm text-gray-600">{t('subtitle')}</p>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('stats.totalReports')}
            </CardTitle>
            <div className="p-2 bg-blue-100 rounded-lg">
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalComplaints}</div>
            <p className="text-xs text-gray-500">Total reports</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('stats.underReview')}
            </CardTitle>
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Clock className="h-4 w-4 text-yellow-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingApproval}</div>
            <p className="text-xs text-gray-500">Awaiting approval</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('stats.verified')}
            </CardTitle>
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{verified}</div>
            <p className="text-xs text-gray-500">{t('status.verified')}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('stats.outstanding')}
            </CardTitle>
            <div className="p-2 bg-red-100 rounded-lg">
              <AlertTriangle className="h-4 w-4 text-red-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {overdueComplaints.length}
            </div>
            <p className="text-xs text-gray-500">{t('stats.overdueDescription')}</p>
          </CardContent>
        </Card>
      </div>

      {/* Outstanding Complaints (Urgent) - Overdue Reports */}
      {overdueComplaints.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-red-600">
              {t('urgentSection.title', { count: overdueComplaints.length })}
            </CardTitle>
            <p className="text-sm text-gray-600">
              {t('urgentSection.description')}
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {overdueComplaints.map((complaint) => {
                const expectedDate = new Date(
                  complaint.expected_completion_date || '',
                );
                const daysOverdue = Math.floor(
                  (new Date().getTime() - expectedDate.getTime()) /
                    (1000 * 60 * 60 * 24),
                );

                return (
                  <div
                    key={complaint.id}
                    className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200"
                  >
                    <div className="flex items-center space-x-3">
                      <AlertCircle className="h-4 w-4 text-red-500" />
                      <div>
                        <p className="font-medium text-sm">
                          {complaint.number}
                        </p>
                        <p className="text-xs text-gray-600">
                          {complaint.description}
                        </p>
                        <p className="text-xs text-red-600 font-medium">
                          {t('urgentSection.daysOverdue', { 
                            days: daysOverdue,
                            plural: daysOverdue !== 1 ? 's' : ''
                          })}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-gray-500">
                        {complaint.no_pma_lif}
                      </p>
                      <p className="text-xs text-gray-500">
                        {complaint.location}
                      </p>
                      <p className="text-xs text-gray-500">
                        {t('urgentSection.expectedLabel')} {expectedDate.toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(complaint.status, complaint.follow_up)}
                      {complaint.follow_up === 'pending_approval' && (
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-xs"
                          onClick={() => handleVerifyComplaint(complaint)}
                          disabled={updateComplaintMutation.isPending}
                        >
                          <CheckCircle className="h-3 w-3 mr-1" />
                          {t('actions.verify')}
                        </Button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}



      {/* Complaints Table */}
      <Card>
        <CardHeader className="flex flex-col space-y-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <CardTitle className="text-lg font-semibold">
                {t('table.title')}
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                {filteredComplaints.length > 0
                  ? `${filteredComplaints.length} complaint${filteredComplaints.length !== 1 ? 's' : ''} found`
                  : 'No complaints found'}
              </p>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                {t('exportReport')}
              </Button>
            </div>
          </div>
          
          {/* Filter Section */}
          <FilterSection
            filters={filters}
            onFilterChange={handleFilterChange}
            searchPlaceholder="Search complaints..."
            renderFilters={
              <>
                {/* Status Filter */}
                <div className="space-y-3">
                  <label className="text-sm font-medium text-gray-700">
                    {t('filters.status')}
                  </label>
                  <Select
                    value={filters.status || 'all'}
                    onValueChange={(value) =>
                      handleFilterChange({
                        ...filters,
                        status: value === 'all' ? '' : value,
                      })
                    }
                  >
                    <SelectTrigger className="w-full border-gray-300 focus:border-gray-400 rounded-lg">
                      <SelectValue placeholder={t('filters.statusPlaceholder')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="pending_approval">
                        {t('status.pendingApproval')}
                      </SelectItem>
                      <SelectItem value="verified">
                        {t('status.verified')}
                      </SelectItem>
                      <SelectItem value="in_progress">
                        {t('status.inProgress')}
                      </SelectItem>
                      <SelectItem value="closed">
                        {t('status.completed')}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* PMA Number Filter */}
                <div className="space-y-3">
                  <label className="text-sm font-medium text-gray-700">
                    {t('filters.pmaNo')}
                  </label>
                  <Input
                    placeholder={t('filters.pmaNoPlaceholder')}
                    value={filters.pma}
                    onChange={(e) =>
                      handleFilterChange({ ...filters, pma: e.target.value })
                    }
                    className="border-gray-300 focus:border-gray-400 rounded-lg"
                  />
                </div>

                {/* Contractor Filter */}
                <div className="space-y-3">
                  <label className="text-sm font-medium text-gray-700">
                    Contractor
                  </label>
                  <Input
                    placeholder="Enter contractor name"
                    value={filters.contractor}
                    onChange={(e) =>
                      handleFilterChange({ ...filters, contractor: e.target.value })
                    }
                    className="border-gray-300 focus:border-gray-400 rounded-lg"
                  />
                </div>

                {/* Date Range Filter */}
                <div className="space-y-3">
                  <label className="text-sm font-medium text-gray-700">
                    Date Range
                  </label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          'w-full justify-start text-left font-normal border-gray-300 hover:border-gray-400 rounded-lg',
                          !filters.dateRange?.from && 'text-gray-500',
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {filters.dateRange?.from ? (
                          filters.dateRange.to ? (
                            <>
                              {format(filters.dateRange.from, 'MMM dd')} -{' '}
                              {format(filters.dateRange.to, 'MMM dd')}
                            </>
                          ) : (
                            format(filters.dateRange.from, 'MMM dd, yyyy')
                          )
                        ) : (
                          'Select date range'
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="range"
                        defaultMonth={filters.dateRange?.from}
                        selected={{
                          from: filters.dateRange?.from,
                          to: filters.dateRange?.to,
                        }}
                        onSelect={(range) =>
                          handleFilterChange({ ...filters, dateRange: range || {} })
                        }
                        numberOfMonths={2}
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </>
            }
            getActiveFilterBadges={(filters, onFilterChange) => (
              <>
                {filters.status && (
                  <Badge
                    variant="secondary"
                    className="h-7 text-xs bg-emerald-100 text-emerald-700 border border-emerald-200 rounded-lg"
                  >
                    Status: {filters.status}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onFilterChange({ ...filters, status: '' })}
                      className="ml-1 h-auto p-0 hover:bg-transparent"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                {filters.pma && (
                  <Badge
                    variant="secondary"
                    className="h-7 text-xs bg-blue-100 text-blue-700 border border-blue-200 rounded-lg"
                  >
                    PMA: {filters.pma}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onFilterChange({ ...filters, pma: '' })}
                      className="ml-1 h-auto p-0 hover:bg-transparent"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                {filters.contractor && (
                  <Badge
                    variant="secondary"
                    className="h-7 text-xs bg-purple-100 text-purple-700 border border-purple-200 rounded-lg"
                  >
                    Contractor: {filters.contractor}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onFilterChange({ ...filters, contractor: '' })}
                      className="ml-1 h-auto p-0 hover:bg-transparent"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                {filters.dateRange?.from && (
                  <Badge
                    variant="secondary"
                    className="h-7 text-xs bg-amber-100 text-amber-700 border border-amber-200 rounded-lg"
                  >
                    Date: {format(filters.dateRange.from, 'MMM dd')}
                    {filters.dateRange.to &&
                      ` - ${format(filters.dateRange.to, 'MMM dd')}`}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onFilterChange({ ...filters, dateRange: {} })}
                      className="ml-1 h-auto p-0 hover:bg-transparent"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
              </>
            )}
            clearAllFilters={handleReset}
          />
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('table.reportId')}</TableHead>
                <TableHead>{t('table.dateSubmitted')}</TableHead>
                <TableHead>{t('table.pmaNumber')}</TableHead>
                <TableHead>{t('table.contractor')}</TableHead>
                <TableHead>{t('table.location')}</TableHead>
                <TableHead>{t('table.status')}</TableHead>
                <TableHead>{t('table.completionDate')}</TableHead>
                <TableHead>{t('table.verificationStatus')}</TableHead>
                <TableHead>{t('table.actions')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentData.map((complaint) => (
                <TableRow key={complaint.id}>
                  <TableCell className="font-medium text-blue-600">
                    {complaint.number}
                  </TableCell>
                  <TableCell>
                    {new Date(complaint.date).toLocaleDateString()}
                  </TableCell>
                  <TableCell>{complaint.no_pma_lif}</TableCell>
                  <TableCell>{complaint.contractor_name}</TableCell>
                  <TableCell>{complaint.location}</TableCell>
                  <TableCell>{getStatusBadge(complaint.status)}</TableCell>
                  <TableCell>
                    {complaint.actual_completion_date
                      ? new Date(
                          complaint.actual_completion_date,
                        ).toLocaleDateString()
                      : 'N/A'}
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(complaint.follow_up, complaint.follow_up)}
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-1">
                      <Button size="sm" variant="outline" className="text-xs">
                        <Eye className="h-3 w-3 mr-1" />
                        {t('actions.view')}
                      </Button>
                      {complaint.follow_up === 'pending_approval' && (
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-xs bg-green-50 text-green-700 hover:bg-green-100"
                          onClick={() => handleVerifyComplaint(complaint)}
                          disabled={updateComplaintMutation.isPending}
                        >
                          <CheckCircle className="h-3 w-3 mr-1" />
                          {t('actions.verify')}
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* Pagination */}
          <div className="flex items-center justify-between mt-4">
            <p className="text-sm text-gray-700">
              {t('pagination.showing', {
                start: startIndex + 1,
                end: Math.min(endIndex, filteredComplaints.length),
                total: filteredComplaints.length,
              })}
            </p>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
                {t('pagination.previous')}
              </Button>

              <div className="flex space-x-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                  (page) => (
                    <Button
                      key={page}
                      variant={currentPage === page ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setCurrentPage(page)}
                      className="w-8 h-8 p-0"
                    >
                      {page}
                    </Button>
                  ),
                )}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                }
                disabled={currentPage === totalPages}
              >
                {t('pagination.next')}
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
